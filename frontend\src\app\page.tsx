// frontend/src/app/page.tsx
'use client';

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Store, MessageSquare, CreditCard } from 'lucide-react';

export default function Home() {
  return (
    <div className="bg-slate-900">
      {/* Hero Section */}
      <section className="relative bg-slate-900 text-white min-h-[80vh] w-full">
        <div className="relative z-10 max-w-7xl mx-auto px-4 py-24">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Content */}
            <div className="text-left">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white">
                Connect, Market, Grow<br />Your Business
              </h1>
              <p className="text-lg md:text-xl text-slate-300 mb-8 max-w-xl">
                Market O'Clock is a B2B2C marketplace that connects suppliers with retailers and businesses through innovative social engagement features.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/register">
                  <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
                    Get Started
                  </Button>
                </Link>
                <Link href="/about">
                  <Button size="lg" variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-800 px-8 py-3">
                    Learn More
                  </Button>
                </Link>
              </div>
            </div>

            {/* Right side - Phone mockup */}
            <div className="flex justify-center lg:justify-end">
              <div className="relative">
                {/* Phone frame */}
                <div className="bg-slate-800 rounded-[2.5rem] p-2 border-4 border-slate-700 shadow-2xl">
                  <div className="bg-slate-900 rounded-[2rem] w-80 h-[600px] relative overflow-hidden">
                    {/* Phone screen content */}
                    <div className="absolute inset-4 bg-slate-700 rounded-[1.5rem] flex items-center justify-center">
                      <span className="text-slate-400 text-center px-4">
                        Marketplace Illustration
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features Section */}
      <section className="py-20 bg-slate-900">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Key Features</h2>
            <p className="text-lg text-slate-400 max-w-2xl mx-auto">
              Discover what makes Market O'Clock the ultimate B2B2C marketplace platform.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Marketplace Feature */}
            <div className="bg-slate-800 rounded-lg p-8 border border-slate-700 hover:border-slate-600 transition-all duration-300 hover:transform hover:scale-105">
              <div className="mb-6">
                <Store className="h-10 w-10 text-blue-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Marketplace</h3>
              <p className="text-slate-400 leading-relaxed">
                Connect suppliers with retailers through our efficient marketplace platform.
              </p>
            </div>

            {/* Microblogs & Blogs Feature */}
            <div className="bg-slate-800 rounded-lg p-8 border border-slate-700 hover:border-slate-600 transition-all duration-300 hover:transform hover:scale-105">
              <div className="mb-6">
                <MessageSquare className="h-10 w-10 text-blue-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Microblogs & Blogs</h3>
              <p className="text-slate-400 leading-relaxed">
                Market products through engaging content with social features.
              </p>
            </div>

            {/* Multi-Payment Support Feature */}
            <div className="bg-slate-800 rounded-lg p-8 border border-slate-700 hover:border-slate-600 transition-all duration-300 hover:transform hover:scale-105">
              <div className="mb-6">
                <CreditCard className="h-10 w-10 text-blue-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Multi-Payment Support</h3>
              <p className="text-slate-400 leading-relaxed">
                Secure and flexible payment options for all transactions.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-slate-800">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to grow your business?
          </h2>
          <p className="text-lg text-slate-400 mb-10 max-w-2xl mx-auto">
            Join Market O'Clock today and connect with suppliers and retailers from various industries.
          </p>
          <Link href="/register">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg">
              Sign Up Now
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
}