// src/components/footer.tsx
'use client';

import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="bg-slate-900 text-slate-300 border-t border-slate-700 py-12 px-6" aria-label="Site footer">
      <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-4 gap-10">
        {/* Company Info */}
        <section aria-labelledby="footer-company-title">
          <h3 id="footer-company-title" className="text-white font-bold mb-4">Market O'Clock</h3>
          <p className="text-slate-400 leading-relaxed">
            Market O'Clock connects suppliers through innovative marketplace solutions with social engagement.
          </p>
        </section>

        {/* Marketplace Links */}
        <nav aria-label="Marketplace" className="text-sm">
          <h4 className="text-white font-semibold mb-4">Marketplace</h4>
          <ul className="space-y-2">
            <li>
              <Link href="/marketplace" className="text-slate-400 hover:text-blue-400 transition-colors">
                Suppliers
              </Link>
            </li>
            <li>
              <Link href="/marketplace" className="text-slate-400 hover:text-blue-400 transition-colors">
                Popular Items
              </Link>
            </li>
            <li>
              <Link href="/marketplace" className="text-slate-400 hover:text-blue-400 transition-colors">
                New Arrivals
              </Link>
            </li>
          </ul>
        </nav>

        {/* Marketplace Links */}
        <nav aria-label="Marketplace" className="text-sm">
          <h4 className="text-white font-semibold mb-4">Marketplace</h4>
          <ul className="space-y-2">
            <li>
              <Link href="/categories" className="text-slate-400 hover:text-blue-400 transition-colors">
                Categories
              </Link>
            </li>
            <li>
              <Link href="/popular" className="text-slate-400 hover:text-blue-400 transition-colors">
                Popular Items
              </Link>
            </li>
            <li>
              <Link href="/new" className="text-slate-400 hover:text-blue-400 transition-colors">
                New Arrivals
              </Link>
            </li>
            <li>
              <Link href="/marketplace" className="text-slate-400 hover:text-blue-400 transition-colors">
                Browse All
              </Link>
            </li>
          </ul>
        </nav>

        {/* Company Links */}
        <nav aria-label="Company" className="text-sm">
          <h4 className="text-white font-semibold mb-4">Company</h4>
          <ul className="space-y-2">
            <li>
              <Link href="/about" className="text-slate-400 hover:text-blue-400 transition-colors">
                About
              </Link>
            </li>
            <li>
              <Link href="/blog" className="text-slate-400 hover:text-blue-400 transition-colors">
                Blogs
              </Link>
            </li>
            <li>
              <Link href="/careers" className="text-slate-400 hover:text-blue-400 transition-colors">
                Careers
              </Link>
            </li>
          </ul>
        </nav>

        {/* Legal & Contact */}
        <nav aria-label="Legal" className="text-sm">
          <h4 className="text-white font-semibold mb-4">Legal</h4>
          <ul className="space-y-2">
            <li>
              <Link href="/terms" className="text-slate-400 hover:text-blue-400 transition-colors">
                Terms of Service
              </Link>
            </li>
            <li>
              <Link href="/privacy" className="text-slate-400 hover:text-blue-400 transition-colors">
                Privacy Policy
              </Link>
            </li>
          </ul>
        </nav>

        {/* Contact */}
        <div className="text-sm">
          <h4 className="text-white font-semibold mb-4">Contact</h4>
          <ul className="space-y-2">
            <li>
              <a href="mailto:<EMAIL>" className="text-slate-400 hover:text-blue-400 transition-colors">
                <EMAIL>
              </a>
            </li>
            <li className="text-slate-500">
              Nairobi, Kenya
            </li>
          </ul>
        </div>
      </div>

      {/* Copyright */}
      <div className="text-center text-sm mt-10 border-t border-slate-700 pt-6">
        <p className="text-slate-500">
          &copy; {new Date().getFullYear()} Market O'Clock. All rights reserved.
        </p>
      </div>
    </footer>
  );
}