// frontend/src/components/layout/header.tsx
'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ModeToggle } from '@/components/mode-toggle';
import { Search, ShoppingCart } from 'lucide-react';
import { MainNav } from './MainNav';
import { MobileNav } from './MobileNav';
import { useAuthStore } from '@/store/authStore';
import { useEffect, useState } from 'react';

export default function Header() {
  const [mounted, setMounted] = useState(false);
  const { isAuthenticated } = useAuthStore();

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <header className="w-full border-b-2 border-blue-500 bg-white shadow-lg text-gray-900 sticky top-0 z-50 h-16" aria-label="Site header">
        <div className="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
          <Link href="/" className="font-bold text-xl text-blue-600">
            Market O'Clock
          </Link>
        </div>
      </header>
    );
  }

  return (
    <header className="w-full border-b-2 border-blue-500 bg-white shadow-lg text-gray-900 sticky top-0 z-50" aria-label="Site header">
      <div className="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
        <div className="flex items-center gap-6">
          <Link
            href="/"
            className="font-bold text-xl text-blue-600 hover:text-blue-700 transition-colors duration-200"
            aria-label="Market O'Clock Home"
          >
            Market O'Clock
          </Link>
          <nav className="hidden md:block" aria-label="Main navigation">
            <MainNav />
          </nav>
        </div>

        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            className="text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors"
            aria-label="Search"
          >
            <Search className="h-5 w-5" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors"
            aria-label="Shopping Cart"
          >
            <ShoppingCart className="h-5 w-5" />
          </Button>
          <ModeToggle />

          {/* Auth Buttons */}
          {mounted && (
            <div className="hidden md:flex items-center gap-3">
              {isAuthenticated ? (
                <Link href="/dashboard">
                  <Button variant="outline" size="sm" className="border-blue-600 text-blue-600 hover:bg-blue-50 hover:text-blue-700">
                    Dashboard
                  </Button>
                </Link>
              ) : (
                <>
                  <Link href="/login">
                    <Button variant="ghost" size="sm" className="text-gray-600 hover:text-blue-600 hover:bg-blue-50">
                      Login
                    </Button>
                  </Link>
                  <Link href="/register">
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                      Sign Up
                    </Button>
                  </Link>
                </>
              )}
            </div>
          )}

          <div className="md:hidden">
            <MobileNav />
          </div>
        </div>
      </div>
    </header>
  );
}