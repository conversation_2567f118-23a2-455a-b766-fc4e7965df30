// Centralized navigation routes for use in MainNav and MobileNav
export const navRoutes = [
  { href: '/', label: 'Home', auth: false },
  { href: '/marketplace', label: 'Marketplace', auth: false },
  { href: '/blog', label: 'Blogs', auth: false },
  { href: '/microblog', label: 'Market Talk', auth: false },
  { href: '/dashboard/feed', label: 'Feed', auth: true },
  { href: '/dashboard/notifications', label: 'Notifications', auth: true },
  { href: '/dashboard/profile', label: 'Profile', auth: true },
  { href: '/dashboard', label: 'Dashboard', auth: true },
];